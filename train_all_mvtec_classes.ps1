# PowerShell script to train all MVTec 2D classes
# Usage: .\train_all_mvtec_classes.ps1

# Define all MVTec 2D classes
$mvtec_classes = @(
    'carpet', 'grid', 'leather', 'tile', 'wood',
    'bottle', 'cable', 'capsule', 'hazelnut', 'metal_nut', 'pill',
    'screw', 'toothbrush', 'transistor', 'zipper'
)

# Common training parameters
$gpu_id = 0
$dataset = "mvtec2d"
$backbone = "resnet18"
$mom = "True"
$oom = "True"
$gamma = 2

# Create results directory with timestamp
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$results_dir = "results_batch_$timestamp"
New-Item -ItemType Directory -Path $results_dir -Force

# Log file for the batch operation
$log_file = "$results_dir\batch_training_log.txt"
"Batch training started at $(Get-Date)" | Out-File -FilePath $log_file

Write-Host "Starting batch training for all MVTec classes..." -ForegroundColor Green
Write-Host "Results will be saved in: $results_dir" -ForegroundColor Yellow

$total_classes = $mvtec_classes.Count
$current_class = 0

foreach ($class_name in $mvtec_classes) {
    $current_class++
    
    Write-Host "`n[$current_class/$total_classes] Training class: $class_name" -ForegroundColor Cyan
    "[$current_class/$total_classes] Starting training for class: $class_name at $(Get-Date)" | Out-File -FilePath $log_file -Append
    
    # Construct the training command
    $command = "python train.py --gpu-id $gpu_id --dataset $dataset --class-name $class_name --backbone $backbone --MOM $mom --OOM $oom --gamma $gamma"
    
    Write-Host "Command: $command" -ForegroundColor Gray
    
    # Execute the training command
    try {
        $start_time = Get-Date
        Invoke-Expression $command
        $end_time = Get-Date
        $duration = $end_time - $start_time
        
        $success_msg = "Successfully completed training for $class_name in $($duration.ToString('hh\:mm\:ss'))"
        Write-Host $success_msg -ForegroundColor Green
        $success_msg | Out-File -FilePath $log_file -Append
        
        # Copy results to batch results directory if they exist
        $class_results_pattern = "*$class_name*"
        if (Test-Path "results") {
            $class_results = Get-ChildItem -Path "results" -Name $class_results_pattern -Directory | Sort-Object LastWriteTime -Descending | Select-Object -First 1
            if ($class_results) {
                $source_path = "results\$class_results"
                $dest_path = "$results_dir\$class_results"
                Copy-Item -Path $source_path -Destination $dest_path -Recurse -Force
                Write-Host "Results copied to: $dest_path" -ForegroundColor Yellow
            }
        }
        
    } catch {
        $error_msg = "Error training $class_name : $($_.Exception.Message)"
        Write-Host $error_msg -ForegroundColor Red
        $error_msg | Out-File -FilePath $log_file -Append
    }
    
    "Completed training for class: $class_name at $(Get-Date)`n" | Out-File -FilePath $log_file -Append
}

Write-Host "`nBatch training completed!" -ForegroundColor Green
"Batch training completed at $(Get-Date)" | Out-File -FilePath $log_file -Append

# Display summary
Write-Host "`nSummary:" -ForegroundColor Yellow
Write-Host "- Total classes trained: $total_classes" -ForegroundColor White
Write-Host "- Results directory: $results_dir" -ForegroundColor White
Write-Host "- Log file: $log_file" -ForegroundColor White
