#!/usr/bin/env python3
"""
Batch training script for all MVTec 2D classes
Usage: python train_all_mvtec_classes.py
"""

import subprocess
import sys
import os
import time
from datetime import datetime
import shutil
import glob

# Define all MVTec 2D classes
mvtec_classes = [
    'carpet', 'grid', 'leather', 'tile', 'wood',
    'bottle', 'cable', 'capsule', 'hazelnut', 'metal_nut', 'pill',
    'screw', 'toothbrush', 'transistor', 'zipper'
]

# Common training parameters
TRAINING_PARAMS = {
    'gpu_id': 0,
    'dataset': 'mvtec2d',
    'backbone': 'resnet18',
    'MOM': True,
    'OOM': True,
    'gamma': 2
}

def create_results_directory():
    """Create a timestamped results directory"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = f"results_batch_{timestamp}"
    os.makedirs(results_dir, exist_ok=True)
    return results_dir

def log_message(log_file, message):
    """Log message to file and print to console"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {message}"
    print(log_entry)
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(log_entry + '\n')

def copy_class_results(class_name, results_dir):
    """Copy the latest results for a specific class to the batch results directory"""
    if not os.path.exists("results"):
        return None
    
    # Find the most recent results directory for this class
    pattern = f"results/*{class_name}*"
    matching_dirs = glob.glob(pattern)
    
    if matching_dirs:
        # Sort by modification time and get the most recent
        latest_dir = max(matching_dirs, key=os.path.getmtime)
        dest_path = os.path.join(results_dir, os.path.basename(latest_dir))
        
        try:
            shutil.copytree(latest_dir, dest_path)
            return dest_path
        except Exception as e:
            print(f"Warning: Could not copy results for {class_name}: {e}")
            return None
    
    return None

def run_training_for_class(class_name, log_file):
    """Run training for a specific class"""
    # Construct the training command
    cmd = [
        'python', 'train.py',
        '--gpu-id', str(TRAINING_PARAMS['gpu_id']),
        '--dataset', TRAINING_PARAMS['dataset'],
        '--class-name', class_name,
        '--backbone', TRAINING_PARAMS['backbone'],
        '--MOM', str(TRAINING_PARAMS['MOM']),
        '--OOM', str(TRAINING_PARAMS['OOM']),
        '--gamma', str(TRAINING_PARAMS['gamma'])
    ]
    
    cmd_str = ' '.join(cmd)
    log_message(log_file, f"Executing: {cmd_str}")
    
    start_time = time.time()
    
    try:
        # Run the training command
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        end_time = time.time()
        duration = end_time - start_time
        duration_str = time.strftime("%H:%M:%S", time.gmtime(duration))
        
        log_message(log_file, f"✓ Successfully completed training for {class_name} in {duration_str}")
        
        # Log stdout if available
        if result.stdout:
            log_message(log_file, f"Output for {class_name}:\n{result.stdout}")
        
        return True, duration
        
    except subprocess.CalledProcessError as e:
        end_time = time.time()
        duration = end_time - start_time
        duration_str = time.strftime("%H:%M:%S", time.gmtime(duration))
        
        error_msg = f"✗ Error training {class_name} after {duration_str}: {e}"
        log_message(log_file, error_msg)
        
        # Log stderr if available
        if e.stderr:
            log_message(log_file, f"Error output for {class_name}:\n{e.stderr}")
        
        return False, duration

def main():
    """Main function to run batch training"""
    print("=" * 60)
    print("MVTec 2D Batch Training Script")
    print("=" * 60)
    
    # Create results directory
    results_dir = create_results_directory()
    log_file = os.path.join(results_dir, "batch_training_log.txt")
    
    log_message(log_file, "Starting batch training for all MVTec classes")
    log_message(log_file, f"Results directory: {results_dir}")
    log_message(log_file, f"Training parameters: {TRAINING_PARAMS}")
    
    total_classes = len(mvtec_classes)
    successful_trainings = 0
    failed_trainings = 0
    total_time = 0
    
    print(f"\nTraining {total_classes} classes with parameters:")
    for key, value in TRAINING_PARAMS.items():
        print(f"  {key}: {value}")
    print()
    
    # Train each class
    for i, class_name in enumerate(mvtec_classes, 1):
        print(f"\n[{i}/{total_classes}] Training class: {class_name}")
        print("-" * 40)
        
        log_message(log_file, f"[{i}/{total_classes}] Starting training for class: {class_name}")
        
        success, duration = run_training_for_class(class_name, log_file)
        total_time += duration
        
        if success:
            successful_trainings += 1
            
            # Copy results
            copied_path = copy_class_results(class_name, results_dir)
            if copied_path:
                log_message(log_file, f"Results copied to: {copied_path}")
        else:
            failed_trainings += 1
        
        log_message(log_file, f"Completed training for class: {class_name}\n")
    
    # Final summary
    total_time_str = time.strftime("%H:%M:%S", time.gmtime(total_time))
    
    print("\n" + "=" * 60)
    print("BATCH TRAINING COMPLETED")
    print("=" * 60)
    print(f"Total classes: {total_classes}")
    print(f"Successful: {successful_trainings}")
    print(f"Failed: {failed_trainings}")
    print(f"Total time: {total_time_str}")
    print(f"Results directory: {results_dir}")
    print(f"Log file: {log_file}")
    
    log_message(log_file, "Batch training completed")
    log_message(log_file, f"Summary - Total: {total_classes}, Successful: {successful_trainings}, Failed: {failed_trainings}")
    log_message(log_file, f"Total execution time: {total_time_str}")

if __name__ == "__main__":
    main()
