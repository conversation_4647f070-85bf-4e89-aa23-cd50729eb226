import os

# 设置目标文件夹路径
target_folder = './datasets/mvtec_anomaly_detection/Aluminum_Camera_Cover'  # 请将此处替换为您的文件夹路径

# 遍历目标文件夹及其所有子文件夹
for root, dirs, files in os.walk(target_folder):
    for file in files:
        # 检查文件是否为 PNG 格式
        if file.endswith('.png'):
            # 构造旧文件的完整路径
            old_file_path = os.path.join(root, file)
            # 构造新文件名
            new_file_name = file.replace('.png', '_mask.png')
            # 构造新文件的完整路径
            new_file_path = os.path.join(root, new_file_name)
            # 重命名文件
            os.rename(old_file_path, new_file_path)
            print(f'Renamed: {old_file_path} to {new_file_path}')
