import cv2
import matplotlib

matplotlib.use("Agg")
import matplotlib.pyplot as plt
import numpy as np
import os
import seaborn as sns
from scipy import stats

##
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA

##
import matplotlib.ticker as mtick
import scienceplots

def plot_sample_cv2(names, imgs, scores_: dict, gts, save_folder=None):
    # get subplot number
    total_number = len(imgs)

    scores = scores_.copy()
    # normarlisze anomalies
    for k, v in scores.items():
        max_value = np.max(v)
        min_value = np.min(v)

        scores[k] = (scores[k] - min_value) / max_value * 255
        scores[k] = scores[k].astype(np.uint8)
    # draw gts
    mask_imgs = []
    for idx in range(total_number):
        gts_ = gts[idx]
        mask_imgs_ = imgs[idx].copy()
        mask_imgs_[gts_ > 0.5] = (255, 0, 0)
        mask_imgs.append(mask_imgs_)

    # save imgs
    for idx in range(total_number):
        cv2.imwrite(os.path.join(save_folder, f'{names[idx]}_ori.jpg'), cv2.cvtColor(imgs[idx], cv2.COLOR_RGB2BGR))
        cv2.imwrite(os.path.join(save_folder, f'{names[idx]}_gt.jpg'), cv2.cvtColor(mask_imgs[idx], cv2.COLOR_RGB2BGR))

        for key in scores:
            heat_map = cv2.applyColorMap(scores[key][idx], cv2.COLORMAP_JET)
            visz_map = cv2.addWeighted(heat_map, 0.5, imgs[idx], 0.5, 0)
            cv2.imwrite(os.path.join(save_folder, f'{names[idx]}_{key}.jpg'),
                        visz_map)


def plot_anomaly_score_distributions(scores: dict, ground_truths_list, save_folder, class_name,
                                   stage="post_training", global_score_range=None):
    """
    Plot anomaly score distributions as volcano plots.

    Args:
        scores: Dictionary of anomaly scores
        ground_truths_list: List of ground truth masks
        save_folder: Directory to save plots
        class_name: Name of the class being analyzed
        stage: Either "pre_training" or "post_training" to distinguish the plots
        global_score_range: Tuple (min_score, max_score) for consistent scaling across plots

    Returns:
        global_score_range: Tuple (min_score, max_score) for use in subsequent plots
    """

    ground_truths = np.stack(ground_truths_list, axis=0)

    N_COUNT = 100000

    # Calculate global score range if not provided
    if global_score_range is None:
        all_scores = []
        for k, v in scores.items():
            layer_score = np.stack(v, axis=0)
            all_scores.extend(layer_score.flatten())
        global_min = np.min(all_scores)
        global_max = np.max(all_scores)
        global_score_range = (global_min, global_max)
    else:
        global_min, global_max = global_score_range

    for k, v in scores.items():
        layer_score = np.stack(v, axis=0)
        normal_score = layer_score[ground_truths == 0]
        abnormal_score = layer_score[ground_truths != 0]

        plt.clf()
        plt.figure(figsize=(8, 6))
        ax = plt.gca()

        # Set font sizes for better readability
        plt.rcParams.update({'font.size': 10})

        with plt.style.context(['science', 'ieee', 'no-latex']):
            # Sample data if needed
            normal_sample = np.random.choice(normal_score, min(N_COUNT, len(normal_score)), replace=len(normal_score) < N_COUNT)
            abnormal_sample = np.random.choice(abnormal_score, min(N_COUNT, len(abnormal_score)), replace=len(abnormal_score) < N_COUNT)

            # Calculate statistics for volcano plot
            # X-axis: log2 fold change (ratio of means)
            normal_mean = np.mean(normal_sample) if len(normal_sample) > 0 else 1e-6
            abnormal_mean = np.mean(abnormal_sample) if len(abnormal_sample) > 0 else 1e-6

            # Avoid division by zero and log of zero
            fold_change = (abnormal_mean + 1e-6) / (normal_mean + 1e-6)
            log2_fold_change = np.log2(fold_change)

            # Y-axis: -log10(p-value) from t-test
            if len(normal_sample) > 1 and len(abnormal_sample) > 1:
                t_stat, p_value = stats.ttest_ind(abnormal_sample, normal_sample)
                # Avoid log of zero
                p_value = max(p_value, 1e-300)
                neg_log10_pvalue = -np.log10(p_value)
            else:
                neg_log10_pvalue = 0

            # Create volcano plot data points
            # For visualization, we'll create a scatter plot with individual data points
            # representing the relationship between score difference and significance

            # Create bins for better visualization
            n_bins = 50
            normal_bins = np.linspace(global_min, global_max, n_bins)
            abnormal_bins = np.linspace(global_min, global_max, n_bins)

            normal_hist, _ = np.histogram(normal_sample, bins=normal_bins, density=True)
            abnormal_hist, _ = np.histogram(abnormal_sample, bins=abnormal_bins, density=True)

            # Calculate bin centers
            bin_centers = (normal_bins[:-1] + normal_bins[1:]) / 2

            # Calculate fold change and significance for each bin
            x_values = []
            y_values = []
            colors = []

            for i in range(len(bin_centers)):
                if normal_hist[i] > 0 and abnormal_hist[i] > 0:
                    # Log2 fold change for this bin
                    fc = np.log2((abnormal_hist[i] + 1e-6) / (normal_hist[i] + 1e-6))

                    # Significance based on difference in densities
                    significance = abs(abnormal_hist[i] - normal_hist[i]) * 10  # Scale for visibility

                    x_values.append(fc)
                    y_values.append(significance)

                    # Color coding based on fold change and significance
                    if abs(fc) > 1 and significance > 0.5:  # Significant change
                        if fc > 0:
                            colors.append('#B91C1C')  # Red for upregulated (higher in abnormal)
                        else:
                            colors.append('#1E3A8A')  # Blue for downregulated (lower in abnormal)
                    else:
                        colors.append('#6B7280')  # Gray for non-significant

            # Create the volcano plot
            scatter = plt.scatter(x_values, y_values, c=colors, alpha=0.7, s=30, edgecolors='none')

            # Add significance thresholds
            plt.axhline(y=0.5, color='gray', linestyle='--', alpha=0.5, linewidth=1)
            plt.axvline(x=1, color='gray', linestyle='--', alpha=0.5, linewidth=1)
            plt.axvline(x=-1, color='gray', linestyle='--', alpha=0.5, linewidth=1)

            # Add overall statistics as text
            plt.text(0.02, 0.98, f'Overall FC: {log2_fold_change:.2f}\nOverall -log10(p): {neg_log10_pvalue:.2f}',
                    transform=ax.transAxes, verticalalignment='top', fontsize=9,
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        # Set labels and title with refined formatting
        plt.xlabel('Log2 Fold Change (Abnormal/Normal)', fontsize=11, fontweight='normal')
        plt.ylabel('Significance Score', fontsize=11, fontweight='normal')
        plt.title(f'{class_name.title()} - Volcano Plot', fontsize=12, fontweight='bold', pad=15)

        # Create custom legend
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='#B91C1C', label='Higher in Abnormal'),
            Patch(facecolor='#1E3A8A', label='Higher in Normal'),
            Patch(facecolor='#6B7280', label='Non-significant')
        ]
        plt.legend(handles=legend_elements, frameon=True, fancybox=True, shadow=False,
                  fontsize=8, loc='upper right', framealpha=0.8,
                  edgecolor='lightgray', facecolor='white')

        # Subtle grid
        plt.grid(True, alpha=0.2, linestyle='-', linewidth=0.5)

        # Clean up axes
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(0.5)
        ax.spines['bottom'].set_linewidth(0.5)

        # Format tick labels
        ax.tick_params(axis='both', which='major', labelsize=9, length=3, width=0.5)
        ax.yaxis.set_major_formatter(mtick.FormatStrFormatter('%.1f'))
        ax.xaxis.set_major_formatter(mtick.FormatStrFormatter('%.1f'))

        save_path = os.path.join(save_folder, f'volcano_{stage}_{class_name}_{k}.jpg')
        plt.savefig(save_path, bbox_inches='tight', dpi=300)

    return global_score_range


def evaluate_and_plot_pre_training(model, dataloader, device, img_dir, class_name):
    """
    Evaluate model before training and generate pre-training scatter plots.

    Args:
        model: The CDO model
        dataloader: Test dataloader
        device: Device to run evaluation on
        img_dir: Directory to save plots
        class_name: Name of the class being analyzed

    Returns:
        global_score_range: Score range for consistent scaling with post-training plots
    """
    import torch  # Import here to avoid circular imports

    # Set model to eval mode
    model.eval_mode()

    scores = []
    gt_mask_list = []

    with torch.no_grad():
        for (data, mask, label, name) in dataloader:
            # Collect ground truth masks
            for i in range(mask.shape[0]):
                gt_mask_list.append(mask[i].squeeze().cpu().numpy())

            # Get anomaly scores
            data = data.to(device)
            outputs = model(data)
            score = model.cal_am(**outputs)
            scores.extend(score)

    # Generate pre-training scatter plots and get global score range
    global_score_range = plot_anomaly_score_distributions(
        {'CDO': scores}, gt_mask_list, save_folder=img_dir,
        class_name=class_name, stage="pre_training"
    )

    return global_score_range


valid_feature_visualization_methods = ['TSNE', 'PCA']


def visualize_feature(features, labels, legends, n_components=3, method='TSNE'):
    assert method in valid_feature_visualization_methods
    assert n_components in [2, 3]

    if method == 'TSNE':
        model = TSNE(n_components=n_components)
    elif method == 'PCA':
        model = PCA(n_components=n_components)

    else:
        raise NotImplementedError

    feat_proj = model.fit_transform(features)

    if n_components == 2:
        ax = scatter_2d(feat_proj, labels)
    elif n_components == 3:
        ax = scatter_3d(feat_proj, labels)
    else:
        raise NotImplementedError

    plt.legend(legends)
    plt.axis('off')


def scatter_3d(feat_proj, label):
    plt.clf()
    ax1 = plt.axes(projection='3d')

    label_unique = np.unique(label)

    for l in label_unique:
        ax1.scatter3D(feat_proj[label == l, 0],
                      feat_proj[label == l, 1],
                      feat_proj[label == l, 2], s=5)

    return ax1


def scatter_2d(feat_proj, label):
    plt.clf()
    ax1 = plt.axes()

    label_unique = np.unique(label)

    for l in label_unique:
        ax1.scatter(feat_proj[label == l, 0],
                    feat_proj[label == l, 1], s=5)

    return ax1
