import cv2
import matplotlib

matplotlib.use("Agg")
import matplotlib.pyplot as plt
import numpy as np
import os
import seaborn as sns

##
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA

##
import scienceplots

def plot_sample_cv2(names, imgs, scores_: dict, gts, save_folder=None):
    # get subplot number
    total_number = len(imgs)

    scores = scores_.copy()
    # normarlisze anomalies
    for k, v in scores.items():
        max_value = np.max(v)
        min_value = np.min(v)

        scores[k] = (scores[k] - min_value) / max_value * 255
        scores[k] = scores[k].astype(np.uint8)
    # draw gts
    mask_imgs = []
    for idx in range(total_number):
        gts_ = gts[idx]
        mask_imgs_ = imgs[idx].copy()
        mask_imgs_[gts_ > 0.5] = (255, 0, 0)
        mask_imgs.append(mask_imgs_)

    # save imgs
    for idx in range(total_number):
        cv2.imwrite(os.path.join(save_folder, f'{names[idx]}_ori.jpg'), cv2.cvtColor(imgs[idx], cv2.COLOR_RGB2BGR))
        cv2.imwrite(os.path.join(save_folder, f'{names[idx]}_gt.jpg'), cv2.cvtColor(mask_imgs[idx], cv2.COLOR_RGB2BGR))

        for key in scores:
            heat_map = cv2.applyColorMap(scores[key][idx], cv2.COLORMAP_JET)
            visz_map = cv2.addWeighted(heat_map, 0.5, imgs[idx], 0.5, 0)
            cv2.imwrite(os.path.join(save_folder, f'{names[idx]}_{key}.jpg'),
                        visz_map)


def plot_anomaly_score_distributions(scores: dict, ground_truths_list, save_folder, class_name,
                                   stage="post_training", global_score_range=None):
    """
    Plot anomaly score distributions.

    Args:
        scores: Dictionary of anomaly scores
        ground_truths_list: List of ground truth masks
        save_folder: Directory to save plots
        class_name: Name of the class being analyzed
        stage: Either "pre_training" or "post_training" to distinguish the plots
        global_score_range: Tuple (min_score, max_score) for consistent scaling across plots

    Returns:
        global_score_range: Tuple (min_score, max_score) for use in subsequent plots
    """

    ground_truths = np.stack(ground_truths_list, axis=0)

    N_COUNT = 100000

    # Calculate global score range if not provided
    if global_score_range is None:
        all_scores = []
        for k, v in scores.items():
            layer_score = np.stack(v, axis=0)
            all_scores.extend(layer_score.flatten())
        global_min = np.min(all_scores)
        global_max = np.max(all_scores)
        global_score_range = (global_min, global_max)
    else:
        global_min, global_max = global_score_range

    for k, v in scores.items():
        layer_score = np.stack(v, axis=0)
        normal_score = layer_score[ground_truths == 0]
        abnormal_score = layer_score[ground_truths != 0]

        plt.clf()
        plt.figure(figsize=(10, 6))

        # Sample data if needed
        normal_sample = np.random.choice(normal_score, min(N_COUNT, len(normal_score)), replace=len(normal_score) < N_COUNT)
        abnormal_sample = np.random.choice(abnormal_score, min(N_COUNT, len(abnormal_score)), replace=len(abnormal_score) < N_COUNT)

        # Create distribution plots
        plt.hist(normal_sample, bins=50, alpha=0.7, label='Normal', color='blue', density=True)
        plt.hist(abnormal_sample, bins=50, alpha=0.7, label='Abnormal', color='red', density=True)

        plt.xlabel('Anomaly Score')
        plt.ylabel('Density')
        plt.title(f'{class_name.title()} - Anomaly Score Distribution ({stage})')
        plt.legend()
        plt.grid(True, alpha=0.3)

        save_path = os.path.join(save_folder, f'distribution_{stage}_{class_name}_{k}.jpg')
        plt.savefig(save_path, bbox_inches='tight', dpi=300)

    return global_score_range


def evaluate_and_plot_pre_training(model, dataloader, device, img_dir, class_name):
    """
    Evaluate model before training and generate pre-training distribution plots.

    Args:
        model: The CDO model
        dataloader: Test dataloader
        device: Device to run evaluation on
        img_dir: Directory to save plots
        class_name: Name of the class being analyzed

    Returns:
        global_score_range: Score range for consistent scaling with post-training plots
    """
    import torch  # Import here to avoid circular imports

    # Set model to eval mode
    model.eval_mode()

    scores = []
    gt_mask_list = []

    with torch.no_grad():
        for (data, mask, label, name) in dataloader:
            # Collect ground truth masks
            for i in range(mask.shape[0]):
                gt_mask_list.append(mask[i].squeeze().cpu().numpy())

            # Get anomaly scores
            data = data.to(device)
            outputs = model(data)
            score = model.cal_am(**outputs)
            scores.extend(score)

    # Generate pre-training distribution plots and get global score range
    global_score_range = plot_anomaly_score_distributions(
        {'CDO': scores}, gt_mask_list, save_folder=img_dir,
        class_name=class_name, stage="pre_training"
    )

    return global_score_range


valid_feature_visualization_methods = ['TSNE', 'PCA']


def visualize_feature(features, labels, legends, n_components=3, method='TSNE'):
    assert method in valid_feature_visualization_methods
    assert n_components in [2, 3]

    if method == 'TSNE':
        model = TSNE(n_components=n_components)
    elif method == 'PCA':
        model = PCA(n_components=n_components)

    else:
        raise NotImplementedError

    feat_proj = model.fit_transform(features)

    if n_components == 2:
        ax = scatter_2d(feat_proj, labels)
    elif n_components == 3:
        ax = scatter_3d(feat_proj, labels)
    else:
        raise NotImplementedError

    plt.legend(legends)
    plt.axis('off')


def scatter_3d(feat_proj, label):
    plt.clf()
    ax1 = plt.axes(projection='3d')

    label_unique = np.unique(label)

    for l in label_unique:
        ax1.scatter3D(feat_proj[label == l, 0],
                      feat_proj[label == l, 1],
                      feat_proj[label == l, 2], s=5)

    return ax1


def scatter_2d(feat_proj, label):
    plt.clf()
    ax1 = plt.axes()

    label_unique = np.unique(label)

    for l in label_unique:
        ax1.scatter(feat_proj[label == l, 0],
                    feat_proj[label == l, 1], s=5)

    return ax1
