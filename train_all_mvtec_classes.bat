@echo off
R<PERSON> Batch script to train all MVTec 2D classes
REM Usage: train_all_mvtec_classes.bat

echo ========================================
echo MVTec 2D Batch Training Script
echo ========================================

REM Create results directory with timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"
set "results_dir=results_batch_%timestamp%"
mkdir "%results_dir%"

REM Log file
set "log_file=%results_dir%\batch_training_log.txt"
echo Batch training started at %date% %time% > "%log_file%"

echo Starting batch training for all MVTec classes...
echo Results will be saved in: %results_dir%
echo.

REM Define common parameters
set gpu_id=0
set dataset=mvtec2d
set backbone=resnet18
set mom=True
set oom=True
set gamma=2

REM Train each class
set /a class_count=0
set /a total_classes=15

for %%c in (carpet grid leather tile wood bottle cable capsule hazelnut metal_nut pill screw toothbrush transistor zipper) do (
    set /a class_count+=1
    echo [!class_count!/%total_classes%] Training class: %%c
    echo [!class_count!/%total_classes%] Starting training for class: %%c at %time% >> "%log_file%"
    
    echo Command: python train.py --gpu-id %gpu_id% --dataset %dataset% --class-name %%c --backbone %backbone% --MOM %mom% --OOM %oom% --gamma %gamma%
    
    python train.py --gpu-id %gpu_id% --dataset %dataset% --class-name %%c --backbone %backbone% --MOM %mom% --OOM %oom% --gamma %gamma%
    
    if !errorlevel! equ 0 (
        echo Successfully completed training for %%c
        echo Successfully completed training for %%c at %time% >> "%log_file%"
    ) else (
        echo Error training %%c
        echo Error training %%c at %time% >> "%log_file%"
    )
    
    echo Completed training for class: %%c at %time% >> "%log_file%"
    echo.
)

echo ========================================
echo Batch training completed!
echo Results directory: %results_dir%
echo Log file: %log_file%
echo ========================================

echo Batch training completed at %date% %time% >> "%log_file%"
pause
